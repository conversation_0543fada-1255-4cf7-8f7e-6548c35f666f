import { NextRequest, NextResponse } from 'next/server';
import { signUp } from '@/lib/auth';

export async function POST(request: NextRequest) {
  try {
    const { username, email, password, name } = await request.json();

    if (!username || !email || !password || !name) {
      return NextResponse.json(
        { error: 'Username, email, password, and name are required' },
        { status: 400 }
      );
    }

    const userProfile = await signUp(username, email, password, name);

    return NextResponse.json({
      success: true,
      user: userProfile,
    });

  } catch (error: any) {
    console.error('Signup error:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to create account' },
      { status: 400 }
    );
  }
}
