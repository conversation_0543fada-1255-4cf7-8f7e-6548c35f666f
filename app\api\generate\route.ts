import { NextResponse } from "next/server";
import { replicate } from "@/lib/replicate";

// Helper function to read a ReadableStream and extract URLs or data
async function readStreamToUrls(stream: ReadableStream): Promise<string[]> {
  const reader = stream.getReader();
  const decoder = new TextDecoder();
  let result = '';

  try {
    while (true) {
      const { done, value } = await reader.read();
      if (done) break;

      // Try to decode as text first
      try {
        result += decoder.decode(value, { stream: true });
      } catch (decodeError) {
        console.log("[DEBUG] Failed to decode stream chunk as text:", decodeError);
        // If it's binary data, it might be the image itself
        // For now, skip binary chunks and continue
        continue;
      }
    }
  } finally {
    reader.releaseLock();
  }

  console.log("[DEBUG] Stream content:", result.substring(0, 200) + "...");

  // Parse the result to extract URLs
  // The stream might contain JSON or plain text URLs
  try {
    const parsed = JSON.parse(result);
    if (Array.isArray(parsed)) {
      return parsed.filter(url => typeof url === 'string' && url.startsWith('http'));
    } else if (typeof parsed === 'string' && parsed.startsWith('http')) {
      return [parsed];
    }
  } catch {
    // If not JSON, try to extract URLs from text
    const urlRegex = /https?:\/\/[^\s]+/g;
    const matches = result.match(urlRegex);
    if (matches) {
      console.log("[DEBUG] Found URLs via regex:", matches);
      return matches;
    }
  }

  return [];
}

export async function POST(req: Request) {
  try {
    const { prompt } = await req.json();

    console.log("[DEBUG] Incoming request payload:", { prompt });

    if (!prompt) {
      console.error("[DEBUG] No prompt provided in request.");
      return NextResponse.json({ error: "Prompt is required" }, { status: 400 });
    }

    // Try different approaches to get image URLs
    let imageUrls: string[] = [];

    try {
      console.log("[DEBUG] Attempting to create prediction and wait for completion");

      // Create a prediction and wait for it to complete
      const prediction = await replicate.predictions.create({
        model: "minimax/image-01",
        input: {
          prompt: prompt,
          aspect_ratio: "1:1",
          number_of_images: 4,
          prompt_optimizer: true,
        },
      });

      console.log("[DEBUG] Prediction created:", prediction.id);

      // Wait for the prediction to complete
      const completedPrediction = await replicate.wait(prediction);
      console.log("[DEBUG] Prediction completed:", completedPrediction.status);
      console.log("[DEBUG] Prediction output:", completedPrediction.output);

      if (completedPrediction.status === 'succeeded' && completedPrediction.output) {
        const output = completedPrediction.output;

        if (Array.isArray(output)) {
          // Filter for valid URLs
          imageUrls = output.filter(item => typeof item === 'string' && item.startsWith('http'));
          console.log("[DEBUG] Found URLs in array:", imageUrls);
        } else if (typeof output === 'string' && output.startsWith('http')) {
          imageUrls = [output];
          console.log("[DEBUG] Found single URL:", imageUrls);
        }
      }

    } catch (predictionError) {
      console.log("[DEBUG] Prediction method failed, trying stream method:", predictionError);

      try {
        console.log("[DEBUG] Attempting to use replicate.stream");
        const stream = await replicate.stream("minimax/image-01", {
          prompt: prompt,
          aspect_ratio: "1:1",
          number_of_images: 4,
          prompt_optimizer: true,
        });

        console.log("[DEBUG] Stream created successfully");

        // Collect all events from the stream
        for await (const event of stream) {
          console.log("[DEBUG] Stream event:", event);
          if (event.event === 'output' && typeof event.data === 'string' && event.data.startsWith('http')) {
            imageUrls.push(event.data);
          }
        }

        console.log("[DEBUG] Collected URLs from stream:", imageUrls);

      } catch (streamError) {
        console.log("[DEBUG] Stream method also failed, trying run method:", streamError);

        // Final fallback to replicate.run
        const output = await replicate.run("minimax/image-01", {
          input: {
            prompt: prompt,
            aspect_ratio: "1:1",
            number_of_images: 4,
            prompt_optimizer: true,
          },
        });

        console.log("[DEBUG] Output from replicate.run:", output, "Type:", typeof output);

        // Process the output to extract URLs from ReadableStreams
        if (Array.isArray(output)) {
          console.log("[DEBUG] Processing array of items");
          for (let i = 0; i < output.length; i++) {
            const item = output[i];
            console.log(`[DEBUG] Processing item ${i}:`, typeof item);

            if (typeof item === 'string' && item.startsWith('http')) {
              // Direct URL
              imageUrls.push(item);
            } else if (item && typeof item === 'object' && 'getReader' in item) {
              // ReadableStream
              console.log(`[DEBUG] Reading stream ${i}`);
              try {
                const urls = await readStreamToUrls(item as ReadableStream);
                imageUrls.push(...urls);
                console.log(`[DEBUG] Extracted ${urls.length} URLs from stream ${i}`);
              } catch (error) {
                console.error(`[DEBUG] Error reading stream ${i}:`, error);
              }
            }
          }
        } else if (output && typeof output === 'object' && 'getReader' in output) {
          // Single ReadableStream
          console.log("[DEBUG] Processing single stream");
          try {
            const urls = await readStreamToUrls(output as ReadableStream);
            imageUrls.push(...urls);
            console.log(`[DEBUG] Extracted ${urls.length} URLs from single stream`);
          } catch (error) {
            console.error("[DEBUG] Error reading single stream:", error);
          }
        }
      }
    }

    console.log("[DEBUG] Final image URLs:", imageUrls);

    if (imageUrls.length === 0) {
      console.error("[DEBUG] No valid image URLs found");
      return NextResponse.json({ error: "No images generated" }, { status: 500 });
    }

    // Stream base64 images to client as they are processed
    const readableStream = new ReadableStream({
      async start(controller) {
        try {
          // Process images sequentially to avoid overwhelming the server
          for (let idx = 0; idx < imageUrls.length; idx++) {
            const url = imageUrls[idx];
            console.log(`[DEBUG] Fetching image ${idx + 1}:`, url);
            try {
              const response = await fetch(url);
              if (!response.ok) {
                throw new Error(`Failed to fetch image: ${response.statusText}`);
              }
              const arrayBuffer = await response.arrayBuffer();
              const buffer = Buffer.from(arrayBuffer);
              const base64 = buffer.toString("base64");
              controller.enqueue(new TextEncoder().encode(`data: ${base64}\n\n`));
              console.log(`[DEBUG] Successfully streamed image ${idx + 1}`);
            } catch (error: any) {
              console.error(`[DEBUG] Error processing image ${idx + 1}:`, error);
              controller.enqueue(new TextEncoder().encode(`event: error\ndata: ${JSON.stringify({ error: error.message })}\n\n`));
            }
          }
          controller.close();
          console.log("[DEBUG] Finished streaming all images.");
        } catch (error) {
          console.error("[DEBUG] Stream error:", error);
          controller.error(error);
        }
      },
    });

    console.log("[DEBUG] Sending response with event-stream headers.");
    return new Response(readableStream, {
      headers: {
        "Content-Type": "text/event-stream",
        "Cache-Control": "no-cache",
      },
    });
  } catch (error: any) {
    console.error("[DEBUG] Top-level error:", error);
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
}
