import { NextResponse } from "next/server";
import { replicate } from "@/lib/replicate";



export async function POST(req: Request) {
  try {
    const { prompt, imageUrl } = await req.json();

    console.log("[DEBUG] Incoming request payload:", { prompt, imageUrl });

    if (!prompt) {
      console.error("[DEBUG] No prompt provided in request.");
      return NextResponse.json({ error: "Prompt is required" }, { status: 400 });
    }

    // Create a prediction and wait for it to complete
    console.log("[DEBUG] Creating prediction and waiting for completion");

    // Prepare input object
    const input: any = {
      prompt: prompt,
      aspect_ratio: "1:1",
      number_of_images: 1,
      prompt_optimizer: true,
    };

    // Add image reference if provided
    if (imageUrl) {
      // Convert relative URL to absolute URL if needed
      const fullImageUrl = imageUrl.startsWith('http')
        ? imageUrl
        : `${process.env.NEXTAUTH_URL || 'http://localhost:3000'}${imageUrl}`;

      input.image = fullImageUrl;
      console.log("[DEBUG] Using reference image:", fullImageUrl);
    }

    const prediction = await replicate.predictions.create({
      model: "minimax/image-01",
      input: input,
    });

    console.log("[DEBUG] Prediction created:", prediction.id);

    // Wait for the prediction to complete
    const completedPrediction = await replicate.wait(prediction);
    console.log("[DEBUG] Prediction completed:", completedPrediction.status);
    console.log("[DEBUG] Prediction output:", completedPrediction.output);

    if (completedPrediction.status !== 'succeeded' || !completedPrediction.output) {
      throw new Error(`Prediction failed with status: ${completedPrediction.status}`);
    }

    const output = completedPrediction.output;
    let imageUrls: string[] = [];

    if (Array.isArray(output)) {
      // Filter for valid URLs
      imageUrls = output.filter(item => typeof item === 'string' && item.startsWith('http'));
      console.log("[DEBUG] Found URLs in array:", imageUrls);
    } else if (typeof output === 'string' && output.startsWith('http')) {
      imageUrls = [output];
      console.log("[DEBUG] Found single URL:", imageUrls);
    } else {
      throw new Error('No valid image URLs found in prediction output');
    }

    console.log("[DEBUG] Final image URLs:", imageUrls);

    if (imageUrls.length === 0) {
      console.error("[DEBUG] No valid image URLs found");
      return NextResponse.json({ error: "No images generated" }, { status: 500 });
    }

    // Return the URLs directly as JSON instead of streaming base64
    console.log("[DEBUG] Returning image URLs directly");
    return NextResponse.json({
      success: true,
      images: imageUrls
    });
  } catch (error: any) {
    console.error("[DEBUG] Top-level error:", error);
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
}
