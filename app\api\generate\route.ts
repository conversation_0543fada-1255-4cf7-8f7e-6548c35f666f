import { NextResponse } from "next/server";
import { replicate } from "@/lib/replicate";

export async function POST(req: Request) {
  try {
    const { prompt } = await req.json();

    console.log("[DEBUG] Incoming request payload:", { prompt });

    if (!prompt) {
      console.error("[DEBUG] No prompt provided in request.");
      return NextResponse.json({ error: "Prompt is required" }, { status: 400 });
    }

    // Use replicate.run to get image URLs directly
    const output = await replicate.run("minimax/image-01", {
      input: {
        prompt: prompt,
        aspect_ratio: "1:1",
        number_of_images: 4,
        prompt_optimizer: true,
      },
    });

    console.log("[DEBUG] Output from replicate.run:", output, "Type:", typeof output);

    // Stream base64 images to client as they are processed
    const readableStream = new ReadableStream({
      async start(controller) {
        try {
          if (!Array.isArray(output)) {
            console.error("[DEBUG] Output is not an array. Actual value:", output);
          }
          await Promise.all(
            (output as any[]).map(async (url, idx) => {
              console.log(`[DEBUG] Processing item ${idx}:`, url, "Type:", typeof url);
              if (typeof url !== "string" || !url.startsWith("http")) {
                console.warn("[DEBUG] Skipping invalid item:", url, "Type:", typeof url);
                return;
              }
              try {
                const response = await fetch(url);
                if (!response.ok) {
                  throw new Error(`Failed to fetch image: ${response.statusText}`);
                }
                const arrayBuffer = await response.arrayBuffer();
                const buffer = Buffer.from(arrayBuffer);
                const base64 = buffer.toString("base64");
                controller.enqueue(new TextEncoder().encode(`data: ${base64}\n\n`));
                console.log(`[DEBUG] Successfully streamed image ${idx + 1}`);
              } catch (error: any) {
                console.error(`[DEBUG] Error processing image ${idx + 1}:`, error);
                controller.enqueue(new TextEncoder().encode(`event: error\ndata: ${JSON.stringify({ error: error.message })}\n\n`));
              }
            })
          );
          controller.close();
          console.log("[DEBUG] Finished streaming all images.");
        } catch (error) {
          console.error("[DEBUG] Stream error:", error);
          controller.error(error);
        }
      },
    });

    console.log("[DEBUG] Sending response with event-stream headers.");
    return new Response(readableStream, {
      headers: {
        "Content-Type": "text/event-stream",
        "Cache-Control": "no-cache",
      },
    });
  } catch (error: any) {
    console.error("[DEBUG] Top-level error:", error);
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
}
