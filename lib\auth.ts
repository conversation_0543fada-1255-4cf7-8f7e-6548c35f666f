import { connectToDatabase } from './mongodb';
import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';

export interface UserProfile {
  uid: string;
  username: string;
  email: string;
  name: string;
  createdAt: Date;
  passwordHash?: string; // Not returned to client
}

const JWT_SECRET = process.env.JWT_SECRET || 'changeme';

function generateToken(user: UserProfile) {
  return jwt.sign(
    { uid: user.uid, username: user.username, email: user.email, name: user.name },
    JWT_SECRET,
    { expiresIn: '7d' }
  );
}

export async function signUp(username: string, email: string, password: string, name: string): Promise<{ user: UserProfile; token: string }> {
  const { db } = await connectToDatabase();

  const existingEmail = await db.collection('users').findOne({ email });
  if (existingEmail) throw new Error('Email already in use');

  const existingUsername = await db.collection('users').findOne({ username });
  if (existingUsername) throw new Error('Username already in use');

  const passwordHash = await bcrypt.hash(password, 10);
  const userProfile: UserProfile = {
    uid: crypto.randomUUID(),
    username,
    email,
    name,
    createdAt: new Date(),
    passwordHash,
  };
  await db.collection('users').insertOne(userProfile);

  // Do not return passwordHash to client
  const { passwordHash: _, ...userNoPass } = userProfile;
  const token = generateToken(userNoPass);

  return { user: userNoPass, token };
}

export async function signIn(email: string, password: string): Promise<{ user: UserProfile; token: string }> {
  const { db } = await connectToDatabase();
  const user = await db.collection('users').findOne({ email });
  if (!user) throw new Error('User not found');
  if (!user.passwordHash) throw new Error('User has no password set');

  const valid = await bcrypt.compare(password, user.passwordHash);
  if (!valid) throw new Error('Invalid password');

  // Map MongoDB user to UserProfile
  const userProfile: UserProfile = {
    uid: user.uid,
    username: user.username,
    email: user.email,
    name: user.name,
    createdAt: new Date(user.createdAt),
  };
  const token = generateToken(userProfile);

  return { user: userProfile, token };
}

export async function logOut(): Promise<void> {
  // With JWT, logout is handled client-side by deleting the token.
  // Optionally, implement a token blacklist if needed.
  return;
}

// There is no onAuthStateChange with MongoDB/JWT; use token verification instead.
export function verifyToken(token: string): UserProfile | null {
  try {
    return jwt.verify(token, JWT_SECRET) as UserProfile;
  } catch {
    return null;
  }
}
