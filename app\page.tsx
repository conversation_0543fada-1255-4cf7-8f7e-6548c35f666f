'use client';

import { useRouter } from 'next/navigation';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Header } from '@/components/header';
import { 
  Sparkles, 
  Upload, 
  MessageCircle, 
  Shirt, 
  Crown, 
  Zap,
  ArrowRight,
  Star
} from 'lucide-react';

export default function Home() {
  const router = useRouter();

  const categories = [
    { name: 'T-Shirt', icon: Shirt, message: "I want to design a custom t-shirt" },
    { name: 'Dress', icon: Crown, message: "I want to design a custom dress" },
    { name: 'Pants', icon: Zap, message: "I want to design custom pants" },
    { name: 'Accessories', icon: Star, message: "I want to design custom accessories" },
  ];

  const navigateToChat = (message?: string) => {
    if (message) {
      localStorage.setItem('initial-message', message);
    }
    router.push('/chat');
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 via-pink-50 to-blue-50">
      <Header />
      
      <main className="pt-16">
        {/* Hero Section */}
        <section className="px-4 py-12 md:py-20">
          <div className="container mx-auto max-w-4xl text-center">
            <div className="mb-8">
              <h1 className="text-4xl md:text-6xl font-bold mb-4">
                Meet <span className="gradient-text">Aurora</span>
              </h1>
              <p className="text-lg md:text-xl text-gray-600 mb-8 leading-relaxed">
                Your AI fashion assistant ready to create unique, personalized designs just for you
              </p>
            </div>

            {/* Action Cards */}
            <div className="grid gap-6 md:grid-cols-3 mb-12">
              <Card 
                className="group cursor-pointer hover:shadow-xl transition-all duration-300 hover:-translate-y-1 border-purple-100"
                onClick={() => navigateToChat("Tell me about Aurora and how she can help me")}
              >
                <CardContent className="p-6 text-center">
                  <div className="w-16 h-16 mx-auto mb-4 rounded-full gradient-bg flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                    <Sparkles className="h-8 w-8 text-white" />
                  </div>
                  <h3 className="font-semibold text-lg mb-2">Aurora Introduction</h3>
                  <p className="text-gray-600 text-sm">Learn how Aurora can transform your fashion ideas</p>
                </CardContent>
              </Card>

              <Card 
                className="group cursor-pointer hover:shadow-xl transition-all duration-300 hover:-translate-y-1 border-purple-100"
                onClick={() => navigateToChat()}
              >
                <CardContent className="p-6 text-center">
                  <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-gradient-to-br from-pink-500 to-rose-500 flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                    <Upload className="h-8 w-8 text-white" />
                  </div>
                  <h3 className="font-semibold text-lg mb-2">Upload Your Idea</h3>
                  <p className="text-gray-600 text-sm">Share an image and let Aurora reimagine it</p>
                </CardContent>
              </Card>

              <Card 
                className="group cursor-pointer hover:shadow-xl transition-all duration-300 hover:-translate-y-1 border-purple-100"
                onClick={() => navigateToChat("Hello Aurora! I'm ready to create something amazing")}
              >
                <CardContent className="p-6 text-center">
                  <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-gradient-to-br from-blue-500 to-purple-500 flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                    <MessageCircle className="h-8 w-8 text-white" />
                  </div>
                  <h3 className="font-semibold text-lg mb-2">Chat With Aurora</h3>
                  <p className="text-gray-600 text-sm">Start a conversation about your design vision</p>
                </CardContent>
              </Card>
            </div>

            {/* Get Started CTA */}
            <Button 
              size="lg" 
              className="gradient-bg hover:opacity-90 transition-opacity text-lg px-8 py-6 rounded-full shadow-lg hover:shadow-xl"
              onClick={() => navigateToChat()}
            >
              Get Started
              <ArrowRight className="ml-2 h-5 w-5" />
            </Button>
          </div>
        </section>

        {/* Categories Section */}
        <section className="px-4 py-12 bg-white/60 backdrop-blur-sm">
          <div className="container mx-auto max-w-4xl">
            <h2 className="text-2xl md:text-3xl font-bold text-center mb-8">
              What Would You Like to Create?
            </h2>
            
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              {categories.map((category) => (
                <Card
                  key={category.name}
                  className="group cursor-pointer hover:shadow-lg transition-all duration-300 hover:-translate-y-1 border-purple-100"
                  onClick={() => navigateToChat(category.message)}
                >
                  <CardContent className="p-6 text-center">
                    <div className="w-12 h-12 mx-auto mb-3 rounded-full bg-gradient-to-br from-purple-100 to-pink-100 flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                      <category.icon className="h-6 w-6 text-purple-600" />
                    </div>
                    <h3 className="font-medium text-sm">{category.name}</h3>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Features Section */}
        <section className="px-4 py-12">
          <div className="container mx-auto max-w-4xl">
            <div className="text-center mb-12">
              <h2 className="text-2xl md:text-3xl font-bold mb-4">
                Why Choose <span className="gradient-text">Unique U</span>?
              </h2>
              <p className="text-gray-600 text-lg">
                Experience the future of personalized fashion design
              </p>
            </div>

            <div className="grid md:grid-cols-3 gap-8">
              <div className="text-center">
                <div className="w-16 h-16 mx-auto mb-4 rounded-full gradient-bg flex items-center justify-center floating-animation">
                  <Sparkles className="h-8 w-8 text-white" />
                </div>
                <h3 className="font-semibold text-lg mb-2">AI-Powered Design</h3>
                <p className="text-gray-600">Advanced AI technology creates unique designs tailored to your style</p>
              </div>

              <div className="text-center">
                <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-gradient-to-br from-pink-500 to-rose-500 flex items-center justify-center floating-animation" style={{animationDelay: '1s'}}>
                  <Upload className="h-8 w-8 text-white" />
                </div>
                <h3 className="font-semibold text-lg mb-2">Easy Upload</h3>
                <p className="text-gray-600">Simply upload your ideas and watch them transform into stunning designs</p>
              </div>

              <div className="text-center">
                <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-gradient-to-br from-blue-500 to-purple-500 flex items-center justify-center floating-animation" style={{animationDelay: '2s'}}>
                  <MessageCircle className="h-8 w-8 text-white" />
                </div>
                <h3 className="font-semibold text-lg mb-2">Interactive Chat</h3>
                <p className="text-gray-600">Collaborate with Aurora through natural conversation</p>
              </div>
            </div>
          </div>
        </section>
      </main>
    </div>
  );
}