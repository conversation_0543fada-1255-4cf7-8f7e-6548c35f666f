'use client';

import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { 
  Sparkles, 
  User, 
  LogIn, 
  LogOut, 
  Info, 
  Mail, 
  Palette,
  Heart
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { useAuth } from '@/components/auth-provider';
import { toast } from 'sonner';
import { LoginDialog } from './login-dialog';
import { useState } from 'react';

interface SideMenuProps {
  onClose: () => void;
}

export function SideMenu({ onClose }: SideMenuProps) {
  const { user, logout } = useAuth();
  const router = useRouter();
  const [isLoginOpen, setIsLoginOpen] = useState(false);

  const handleNewDesign = () => {
    localStorage.removeItem('chat-messages');
    router.push('/chat');
    onClose();
  };

  const handleLogout = () => {
    logout();
    localStorage.removeItem('chat-messages');
    toast.success('Logged out successfully');
    onClose();
  };

  return (
    <div className="flex flex-col h-full py-6">
      <div className="flex items-center space-x-2 px-6 mb-8">
        <div className="w-8 h-8 rounded-full gradient-bg flex items-center justify-center">
          <Sparkles className="h-4 w-4 text-white" />
        </div>
        <span className="text-xl font-bold gradient-text">Unique U</span>
      </div>

      <div className="flex-1 px-6">
        <div className="space-y-2">
          <Button
            variant="ghost"
            className="w-full justify-start space-x-3 text-left"
            onClick={handleNewDesign}
          >
            <Palette className="h-4 w-4" />
            <span>New Design</span>
          </Button>

          <Button
            variant="ghost"
            className="w-full justify-start space-x-3 text-left"
            asChild
          >
            <Link href="/saved-designs" onClick={onClose}>
              <Heart className="h-4 w-4" />
              <span>Your Designs</span>
            </Link>
          </Button>

          <Separator className="my-4" />

          {user ? (
            <div className="space-y-2">
              <div className="flex items-center space-x-3 px-3 py-2 rounded-md bg-purple-50">
                <User className="h-4 w-4 text-purple-600" />
                <span className="text-sm font-medium text-purple-900">{user.name}</span>
              </div>
              <Button
                variant="ghost"
                className="w-full justify-start space-x-3 text-left"
                onClick={handleLogout}
              >
                <LogOut className="h-4 w-4" />
                <span>Logout</span>
              </Button>
            </div>
          ) : (
            <Button
              variant="ghost"
              className="w-full justify-start space-x-3 text-left"
              onClick={() => setIsLoginOpen(true)}
            >
              <LogIn className="h-4 w-4" />
              <span>Login</span>
            </Button>
          )}

          <Separator className="my-4" />

          <Button
            variant="ghost"
            className="w-full justify-start space-x-3 text-left"
            asChild
          >
            <Link href="/about" onClick={onClose}>
              <Info className="h-4 w-4" />
              <span>About Us</span>
            </Link>
          </Button>

          <Button
            variant="ghost"
            className="w-full justify-start space-x-3 text-left"
            asChild
          >
            <Link href="/contact" onClick={onClose}>
              <Mail className="h-4 w-4" />
              <span>Contact Us</span>
            </Link>
          </Button>
        </div>
      </div>
      <LoginDialog open={isLoginOpen} onOpenChange={setIsLoginOpen} />
    </div>
  );
}
