'use client';

import { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Header } from '@/components/header';
import { useAuth } from '@/components/auth-provider';
import { Heart, MessageCircle, Palette } from 'lucide-react';
import Link from 'next/link';

export default function SavedDesigns() {
  const [savedChats, setSavedChats] = useState<any[]>([]);
  const { user } = useAuth();

  useEffect(() => {
    // In a real app, you'd fetch saved chats from a database
    // For now, we'll just show the current chat if it exists
    const currentChat = localStorage.getItem('chat-messages');
    if (currentChat) {
      try {
        const messages = JSON.parse(currentChat);
        const designMessages = messages.filter((msg: any) => msg.images && msg.images.length > 0);
        setSavedChats(designMessages);
      } catch (error) {
        console.error('Error loading saved chats:', error);
      }
    }
  }, []);

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 via-pink-50 to-blue-50">
      <Header />
      
      <div className="pt-16">
        <div className="container mx-auto max-w-4xl px-4 py-8">
          <h1 className="text-3xl font-bold gradient-text mb-8">Your Designs</h1>

          {!user ? (
            <Card className="text-center py-12">
              <CardContent>
                <Heart className="h-16 w-16 mx-auto mb-4 text-gray-400" />
                <h2 className="text-xl font-semibold mb-2">Login to Save Your Designs</h2>
                <p className="text-gray-600 mb-6">
                  Create an account to save and access your design history across devices.
                </p>
                <Button asChild className="gradient-bg hover:opacity-90">
                  <Link href="/login">Login Now</Link>
                </Button>
              </CardContent>
            </Card>
          ) : savedChats.length === 0 ? (
            <Card className="text-center py-12">
              <CardContent>
                <Palette className="h-16 w-16 mx-auto mb-4 text-gray-400" />
                <h2 className="text-xl font-semibold mb-2">No Saved Designs Yet</h2>
                <p className="text-gray-600 mb-6">
                  Start creating with Aurora to see your designs here!
                </p>
                <Button asChild className="gradient-bg hover:opacity-90">
                  <Link href="/chat">
                    <MessageCircle className="h-4 w-4 mr-2" />
                    Start Designing
                  </Link>
                </Button>
              </CardContent>
            </Card>
          ) : (
            <div className="space-y-6">
              {savedChats.map((chat, index) => (
                <Card key={index}>
                  <CardContent className="p-6">
                    <div className="flex justify-between items-start mb-4">
                      <h3 className="font-semibold text-lg">Design Session {index + 1}</h3>
                      <span className="text-sm text-gray-500">
                        {new Date(chat.timestamp).toLocaleDateString()}
                      </span>
                    </div>
                    
                    <p className="text-gray-600 mb-4">{chat.content}</p>
                    
                    {chat.images && (
                      <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                        {chat.images.map((image: string, imgIndex: number) => (
                          <img
                            key={imgIndex}
                            src={image}
                            alt={`Design ${imgIndex + 1}`}
                            className="w-full h-32 object-cover rounded-lg border border-purple-100"
                          />
                        ))}
                      </div>
                    )}
                  </CardContent>
                </Card>
              ))}
              
              <div className="text-center">
                <Button asChild className="gradient-bg hover:opacity-90">
                  <Link href="/chat">
                    <MessageCircle className="h-4 w-4 mr-2" />
                    Continue Designing
                  </Link>
                </Button>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}