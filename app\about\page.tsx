import Link from 'next/link';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Header } from '@/components/header';
import { Sparkles, Heart, Users, Star, Mail } from 'lucide-react';

export default function About() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 via-pink-50 to-blue-50">
      <Header />
      
      <div className="pt-16">
        <div className="container mx-auto max-w-4xl px-4 py-8">
          <div className="text-center mb-12">
            <h1 className="text-4xl font-bold gradient-text mb-4">About Unique U</h1>
            <p className="text-xl text-gray-600">
              Revolutionizing fashion design with AI-powered creativity
            </p>
          </div>

          <div className="grid gap-8 md:grid-cols-2 mb-12">
            <Card>
              <CardContent className="p-8">
                <div className="w-16 h-16 mx-auto mb-4 rounded-full gradient-bg flex items-center justify-center">
                  <Sparkles className="h-8 w-8 text-white" />
                </div>
                <h2 className="text-2xl font-bold mb-4 text-center">Our Mission</h2>
                <p className="text-gray-600 leading-relaxed">
                  At Unique U, we believe everyone deserves to express their individuality through fashion. 
                  Our AI-powered platform makes custom design accessible, affordable, and incredibly personal.
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-8">
                <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-gradient-to-br from-pink-500 to-rose-500 flex items-center justify-center">
                  <Heart className="h-8 w-8 text-white" />
                </div>
                <h2 className="text-2xl font-bold mb-4 text-center">Our Vision</h2>
                <p className="text-gray-600 leading-relaxed">
                  To create a world where every piece of clothing tells a unique story, 
                  where creativity knows no bounds, and where personal style becomes a 
                  form of artistic self-expression.
                </p>
              </CardContent>
            </Card>
          </div>

          <Card className="mb-12">
            <CardContent className="p-8">
              <h2 className="text-3xl font-bold gradient-text mb-6 text-center">Meet Aurora</h2>
              <div className="flex flex-col md:flex-row items-center space-y-6 md:space-y-0 md:space-x-8">
                <div className="w-32 h-32 rounded-full gradient-bg flex items-center justify-center floating-animation">
                  <Sparkles className="h-16 w-16 text-white" />
                </div>
                <div className="flex-1 text-center md:text-left">
                  <p className="text-lg text-gray-600 leading-relaxed mb-4">
                    Aurora is more than just an AI assistant – she's your creative partner in the journey 
                    of fashion design. Trained on thousands of fashion trends, color theories, and style 
                    combinations, Aurora understands your vision and brings it to life.
                  </p>
                  <p className="text-gray-600 leading-relaxed">
                    Whether you're looking for casual wear, formal attire, or something completely unique, 
                    Aurora guides you through every step of the design process with intelligence, 
                    creativity, and style.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <div className="grid gap-6 md:grid-cols-3 mb-12">
            <Card className="text-center">
              <CardContent className="p-6">
                <Users className="h-12 w-12 mx-auto mb-4 text-purple-600" />
                <h3 className="font-bold text-lg mb-2">10,000+</h3>
                <p className="text-gray-600">Happy Customers</p>
              </CardContent>
            </Card>
            
            <Card className="text-center">
              <CardContent className="p-6">
                <Star className="h-12 w-12 mx-auto mb-4 text-purple-600" />
                <h3 className="font-bold text-lg mb-2">50,000+</h3>
                <p className="text-gray-600">Designs Created</p>
              </CardContent>
            </Card>
            
            <Card className="text-center">
              <CardContent className="p-6">
                <Heart className="h-12 w-12 mx-auto mb-4 text-purple-600" />
                <h3 className="font-bold text-lg mb-2">99%</h3>
                <p className="text-gray-600">Satisfaction Rate</p>
              </CardContent>
            </Card>
          </div>

          <Card className="text-center">
            <CardContent className="p-8">
              <h2 className="text-2xl font-bold mb-4">Ready to Start Creating?</h2>
              <p className="text-gray-600 mb-6">
                Join thousands of fashion enthusiasts who have discovered their unique style with Aurora.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button asChild className="gradient-bg hover:opacity-90" size="lg">
                  <Link href="/chat">
                    <Sparkles className="h-4 w-4 mr-2" />
                    Chat with Aurora
                  </Link>
                </Button>
                <Button variant="outline" asChild size="lg">
                  <Link href="/contact">
                    <Mail className="h-4 w-4 mr-2" />
                    Contact Us
                  </Link>
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}