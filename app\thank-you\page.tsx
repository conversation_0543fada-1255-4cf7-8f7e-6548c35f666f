import Link from 'next/link';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Header } from '@/components/header';
import { CheckCircle, Home, MessageCircle } from 'lucide-react';

export default function ThankYou() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 via-pink-50 to-blue-50">
      <Header />
      
      <div className="pt-16">
        <div className="container mx-auto max-w-2xl px-4 py-16">
          <Card className="text-center py-12">
            <CardContent>
              <div className="w-20 h-20 mx-auto mb-6 rounded-full gradient-bg flex items-center justify-center">
                <CheckCircle className="h-10 w-10 text-white" />
              </div>
              
              <h1 className="text-3xl font-bold gradient-text mb-4">
                Thank You!
              </h1>
              
              <p className="text-lg text-gray-600 mb-6 leading-relaxed">
                Your order request has been successfully submitted. We'll contact you within 24 hours with pricing details and production timeline.
              </p>
              
              <div className="space-y-4">
                <p className="text-sm text-gray-500">
                  Check your email for order confirmation details.
                </p>
                
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Button asChild className="gradient-bg hover:opacity-90">
                    <Link href="/">
                      <Home className="h-4 w-4 mr-2" />
                      Back to Home
                    </Link>
                  </Button>
                  
                  <Button variant="outline" asChild>
                    <Link href="/chat">
                      <MessageCircle className="h-4 w-4 mr-2" />
                      Create More Designs
                    </Link>
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}