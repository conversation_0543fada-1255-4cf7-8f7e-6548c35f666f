'use client';

import { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { AuthProvider } from './auth-provider';

interface CartItem {
  id: string;
  title: string;
  image: string;
  timestamp: Date;
}

interface AppContextType {
  cart: CartItem[];
  addToCart: (item: Omit<CartItem, 'id' | 'timestamp'>) => void;
  removeFromCart: (id: string) => void;
  clearCart: () => void;
  generationCount: number;
  incrementGeneration: () => void;
}

const AppContext = createContext<AppContextType | undefined>(undefined);

function AppProvider({ children }: { children: ReactNode }) {
  const [cart, setCart] = useState<CartItem[]>([]);
  const [generationCount, setGenerationCount] = useState(0);

  useEffect(() => {
    // Only run on client side
    if (typeof window === 'undefined') return;
    
    try {
      // Load cart from localStorage
      const savedCart = localStorage.getItem('unique-u-cart');
      if (savedCart) {
        const parsedCart = JSON.parse(savedCart);
        setCart(parsedCart.map((item: any) => ({
          ...item,
          timestamp: new Date(item.timestamp)
        })));
      }

      // Load generation count
      const savedCount = localStorage.getItem('unique-u-generations');
      if (savedCount) {
        setGenerationCount(parseInt(savedCount));
      }
    } catch (error) {
      console.error('Error loading from localStorage:', error);
    }
  }, []);

  const addToCart = (item: Omit<CartItem, 'id' | 'timestamp'>) => {
    if (typeof window === 'undefined') return;
    const newItem: CartItem = {
      ...item,
      id: Date.now().toString(),
      timestamp: new Date(),
    };
    const updatedCart = [...cart, newItem];
    setCart(updatedCart);
    localStorage.setItem('unique-u-cart', JSON.stringify(updatedCart));
  };

  const removeFromCart = (id: string) => {
    if (typeof window === 'undefined') return;
    const updatedCart = cart.filter(item => item.id !== id);
    setCart(updatedCart);
    localStorage.setItem('unique-u-cart', JSON.stringify(updatedCart));
  };

  const clearCart = () => {
    if (typeof window === 'undefined') return;
    setCart([]);
    localStorage.removeItem('unique-u-cart');
  };

  const incrementGeneration = () => {
    const newCount = generationCount + 1;
    setGenerationCount(newCount);
    localStorage.setItem('unique-u-generations', newCount.toString());
  };

  return (
    <AppContext.Provider
      value={{
        cart,
        addToCart,
        removeFromCart,
        clearCart,
        generationCount,
        incrementGeneration,
      }}
    >
      {children}
    </AppContext.Provider>
  );
}

export function Providers({ children }: { children: ReactNode }) {
  return (
    <AuthProvider>
      <AppProvider>
        {children}
      </AppProvider>
    </AuthProvider>
  );
}

export const useApp = () => {
  const context = useContext(AppContext);
  if (context === undefined) {
    throw new Error('useApp must be used within a Providers component');
  }
  return context;
};