import { NextRequest, NextResponse } from 'next/server';
import { createOrder } from '@/lib/database';
import nodemailer from 'nodemailer';

export async function POST(request: NextRequest) {
  try {
    const orderData = await request.json();

    if (!orderData.userId || !orderData.items || !orderData.customerInfo) {
      return NextResponse.json(
        { error: 'Missing required order data' },
        { status: 400 }
      );
    }

    // Create order in database
    const orderId = await createOrder({
      ...orderData,
      status: 'pending',
      timestamp: new Date(),
    });

    // Send email notification
    try {
      const transporter = nodemailer.createTransport({
        host: process.env.SMTP_HOST,
        port: parseInt(process.env.SMTP_PORT || '587'),
        secure: false,
        auth: {
          user: process.env.SMTP_USER,
          pass: process.env.SMTP_PASS,
        },
      });

      const emailContent = `
        New Order Received - Unique U
        
        Order ID: ${orderId}
        Customer: ${orderData.customerInfo.name}
        Email: ${orderData.customerInfo.email}
        Phone: ${orderData.customerInfo.phone}
        Address: ${orderData.customerInfo.address}
        
        Items: ${orderData.items.length} design(s)
        
        Notes: ${orderData.customerInfo.notes || 'None'}
        
        Please contact the customer within 24 hours with pricing and timeline.
      `;

      await transporter.sendMail({
        from: process.env.SMTP_USER,
        to: process.env.ADMIN_EMAIL,
        subject: `New Order - ${orderData.customerInfo.name}`,
        text: emailContent,
      });
    } catch (emailError) {
      console.error('Email sending failed:', emailError);
      // Don't fail the order creation if email fails
    }

    return NextResponse.json({
      success: true,
      orderId,
    });

  } catch (error) {
    console.error('Order creation error:', error);
    return NextResponse.json(
      { error: 'Failed to create order' },
      { status: 500 }
    );
  }
}