'use client';

import * as React from 'react';
import { Dialog, DialogContent } from '@/components/ui/dialog';

interface ImageModalProps {
  open: boolean;
  images: string[];
  onOpenChange: (open: boolean) => void;
}

export function ImageModal({ open, images, onOpenChange }: ImageModalProps) {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl flex flex-col items-center gap-4">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 w-full">
          {images.map((image, idx) => (
            <img
              key={idx}
              src={image}
              alt={`Generated design ${idx + 1}`}
              className="w-full h-64 object-cover rounded-lg border border-purple-100"
            />
          ))}
        </div>
      </DialogContent>
    </Dialog>
  );
}
