'use client';

import * as React from 'react';
import { Dialog, DialogContent } from '@/components/ui/dialog';

interface ImageModalProps {
  open: boolean;
  images: string[];
  onOpenChange: (open: boolean) => void;
}

export function ImageModal({ open, images, onOpenChange }: ImageModalProps) {
  console.log('[DEBUG] ImageModal: open =', open, 'images.length =', images.length);

  if (images.length === 0) {
    console.log('[DEBUG] ImageModal: No images to display');
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <div className="p-4">
          <h2 className="text-xl font-semibold mb-4">Generated Images ({images.length})</h2>
          {images.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-gray-500">No images to display</p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {images.map((image, idx) => (
                <div key={idx} className="relative">
                  <img
                    src={image}
                    alt={`Generated design ${idx + 1}`}
                    className="w-full h-64 object-cover rounded-lg border border-purple-100"
                    onLoad={() => console.log(`[DEBUG] Image ${idx + 1} loaded successfully`)}
                    onError={(e) => console.error(`[DEBUG] Image ${idx + 1} failed to load:`, e)}
                  />
                  <div className="absolute top-2 right-2 bg-black/50 text-white px-2 py-1 rounded text-sm">
                    {idx + 1}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}
