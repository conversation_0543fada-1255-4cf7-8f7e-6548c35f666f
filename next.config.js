/** @type {import('next').NextConfig} */
const nextConfig = {
  eslint: {
    ignoreDuringBuilds: true,
  },
  images: { unoptimized: true },
  // Add webpack configuration to handle Firebase dependencies and undici
  webpack: (config, { isServer, webpack }) => {
    // Fixes npm packages that depend on `undici`
    if (!isServer) {
      config.resolve.fallback = {
        ...config.resolve.fallback,
        fs: false,
        net: false,
        tls: false,
        dns: 'mock',
      };
    }

    // Add rule to handle undici package
    config.module.rules.push({
      test: /[\\/]node_modules[\\/](@firebase[\\/]storage[\\/]node_modules[\\/]undici|firebase[\\/]node_modules[\\/]undici|undici)[\\/]/,
      use: [
        {
          loader: 'babel-loader',
          options: {
            presets: ['next/babel'],
            plugins: [
              ['@babel/plugin-proposal-class-properties', { loose: true }],
              ['@babel/plugin-proposal-private-methods', { loose: true }],
              ['@babel/plugin-proposal-private-property-in-object', { loose: true }]
            ]
          }
        }
      ]
    });

    return config;
  },
  // Disable static optimization for API routes
  experimental: {
    serverComponentsExternalPackages: ['firebase-admin'],
  },
};

module.exports = nextConfig;
