'use client';

import { useState, useEffect, useRef } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Header } from '@/components/header';
import { LoginDialog } from '@/components/login-dialog';
import { useApp } from '@/components/providers';
import { useAuth } from '@/components/auth-provider';
import { ImageModal } from '@/components/image-modal';
import {
  Upload,
  Sparkles,
} from 'lucide-react';
import { toast } from 'sonner';

export default function Chat() {
  const [input, setInput] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [showLoginDialog, setShowLoginDialog] = useState(false);
  const [imageModalOpen, setImageModalOpen] = useState(false);
  const [modalImages, setModalImages] = useState<string[]>([]);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { addToCart, generationCount, incrementGeneration } = useApp();
  const { user } = useAuth();
  const router = useRouter();

  useEffect(() => {
    // Check if login dialog should be shown
    if (generationCount >= 6 && !user) {
      setShowLoginDialog(true);
    }
  }, [generationCount, user]);

  const generateImages = async () => {
    if (!input.trim() || isLoading) return;

    setIsLoading(true);
    setModalImages([]);

    try {
      const response = await fetch('/api/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ prompt: input }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      console.log('[DEBUG] Frontend: Received response:', data);

      if (data.success && data.images && Array.isArray(data.images)) {
        console.log('[DEBUG] Frontend: Setting modal images:', data.images.length);
        setModalImages(data.images);
        setImageModalOpen(true);
        incrementGeneration();

        if (generationCount + 1 >= 6 && !user) {
          setTimeout(() => setShowLoginDialog(true), 1000);
        }
      } else {
        throw new Error(data.error || 'Failed to generate images');
      }

      if (generationCount + 1 >= 6 && !user) {
        setTimeout(() => setShowLoginDialog(true), 1000);
      }

    } catch (error) {
      console.error('Generation error:', error);
      toast.error('Failed to generate images. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      // In a real app, you'd upload the file and get a URL
      toast.success('Image uploaded! Analyzing your design inspiration...');
      setInput('I uploaded an image for inspiration. Can you create designs based on this?');
    }
  };

  const handleAddToCart = (image: string, index: number) => {
    addToCart({
      title: `Custom Design ${index + 1}`,
      image,
    });
    toast.success('Added to cart!');
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 via-pink-50 to-blue-50">
      <Header />

      <div className="pt-16 pb-24">
        <div className="container mx-auto max-w-4xl px-4 py-6">
          <div className="flex flex-col items-center justify-center h-[calc(100vh-200px)]">
            <h1 className="text-4xl font-bold text-gray-800 mb-4">AI-Powered Design Studio</h1>
            <p className="text-lg text-gray-600 mb-8">Bring your ideas to life. Describe your vision and let our AI create it.</p>
            {isLoading && (
              <div className="flex flex-col items-center">
                <Sparkles className="h-12 w-12 text-purple-500 animate-pulse mb-4" />
                <p className="text-lg text-gray-600">Generating your designs...</p>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Fixed Input Area */}
      <div className="fixed bottom-0 left-0 right-0 bg-white/90 backdrop-blur-md border-t border-purple-100 p-4">
        <div className="container mx-auto max-w-4xl">
          <div className="flex items-center space-x-2">
            <input
              ref={fileInputRef}
              type="file"
              accept="image/*"
              onChange={handleFileUpload}
              className="hidden"
            />
            <Button
              variant="outline"
              size="icon"
              onClick={() => fileInputRef.current?.click()}
              disabled={isLoading}
            >
              <Upload className="h-4 w-4" />
            </Button>

            <div className="flex-1 flex items-center space-x-2">
              <Input
                value={input}
                onChange={(e) => setInput(e.target.value)}
                placeholder="Describe your design idea..."
                onKeyPress={(e) => e.key === 'Enter' && generateImages()}
                disabled={isLoading}
                className="flex-1"
              />
              <Button
                onClick={generateImages}
                disabled={isLoading || !input.trim()}
                className="gradient-bg hover:opacity-90"
              >
                <Sparkles className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
      </div>

      <LoginDialog
        open={showLoginDialog}
        onOpenChange={setShowLoginDialog}
      />
      <ImageModal
        open={imageModalOpen}
        images={modalImages}
        onOpenChange={setImageModalOpen}
      />
    </div>
  );
}
