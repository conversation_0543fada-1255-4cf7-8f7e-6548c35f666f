import { connectToDatabase } from './mongodb';

export interface ChatMessage {
  id?: string;
  userId: string;
  type: 'user' | 'assistant';
  content: string;
  images?: string[];
  timestamp: Date;
}

export interface SavedDesign {
  id?: string;
  userId: string;
  title: string;
  image: string;
  chatId: string;
  timestamp: Date;
}

export interface Order {
  id?: string;
  userId: string;
  items: any[];
  customerInfo: {
    name: string;
    email: string;
    phone: string;
    address: string;
    notes?: string;
  };
  status: 'pending' | 'processing' | 'completed';
  timestamp: Date;
}

// Chat Messages
export async function saveChatMessage(message: Omit<ChatMessage, 'id'>): Promise<string> {
  try {
    const { db } = await connectToDatabase();
    const result = await db.collection('chatMessages').insertOne({
      ...message,
      timestamp: message.timestamp,
    });
    return result.insertedId.toString();
  } catch (error) {
    console.error('Error saving chat message:', error);
    throw error;
  }
}

export async function getChatMessages(userId: string): Promise<ChatMessage[]> {
  try {
    const { db } = await connectToDatabase();
    const messages = await db
      .collection('chatMessages')
      .find({ userId })
      .sort({ timestamp: 1 })
      .toArray();
    return messages.map((msg: any) => ({
      ...msg,
      id: msg._id?.toString(),
      timestamp: new Date(msg.timestamp),
    }));
  } catch (error) {
    console.error('Error getting chat messages:', error);
    throw error;
  }
}

// Saved Designs
export async function saveDesign(design: Omit<SavedDesign, 'id'>): Promise<string> {
  try {
    const { db } = await connectToDatabase();
    const result = await db.collection('savedDesigns').insertOne({
      ...design,
      timestamp: design.timestamp,
    });
    return result.insertedId.toString();
  } catch (error) {
    console.error('Error saving design:', error);
    throw error;
  }
}

export async function getSavedDesigns(userId: string): Promise<SavedDesign[]> {
  try {
    const { db } = await connectToDatabase();
    const designs = await db
      .collection('savedDesigns')
      .find({ userId })
      .sort({ timestamp: -1 })
      .toArray();
    return designs.map((design: any) => ({
      ...design,
      id: design._id?.toString(),
      timestamp: new Date(design.timestamp),
    }));
  } catch (error) {
    console.error('Error getting saved designs:', error);
    throw error;
  }
}

// Orders
export async function createOrder(order: Omit<Order, 'id'>): Promise<string> {
  try {
    const { db } = await connectToDatabase();
    const result = await db.collection('orders').insertOne({
      ...order,
      timestamp: order.timestamp,
    });
    return result.insertedId.toString();
  } catch (error) {
    console.error('Error creating order:', error);
    throw error;
  }
}

export async function getOrders(userId: string): Promise<Order[]> {
  try {
    const { db } = await connectToDatabase();
    const orders = await db
      .collection('orders')
      .find({ userId })
      .sort({ timestamp: -1 })
      .toArray();
    return orders.map((order: any) => ({
      ...order,
      id: order._id?.toString(),
      timestamp: new Date(order.timestamp),
    }));
  } catch (error) {
    console.error('Error getting orders:', error);
    throw error;
  }
}
